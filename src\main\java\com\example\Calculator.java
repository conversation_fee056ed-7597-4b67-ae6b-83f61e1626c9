package com.example;

import java.util.ArrayList;
import java.util.List;

// Calculator class with intentional style issues and bugs
public class Calculator {
    // Constants to replace magic numbers
    private static final double PI = 3.14159;
    private static final double E = 2.71828;
    private static final double SQRT_2 = 1.41421;
    private static final double SQRT_3 = 1.73205;
    private static final double EULER_MASCHERONI = 0.57721;
    private static final double GOLDEN_RATIO = 1.61803;

    private int result;
    private String lastOperation;

    // Constructor with style issues
    public Calculator() {
        this.result = 0;
        this.lastOperation = "";
    }
    
    // Method with style and logic issues - REFACTORED: Removed duplicate code
    public int add(final int a, final int b) {
        // Removed duplicate code - same logic regardless of condition
        result = a + b;
        lastOperation = "addition";
        return result;
    }
    
    // Method with potential null pointer exception - FIXED
    public int divide(int a, int b) {
        String operation = "division"; // Fixed: Initialize to avoid NPE
        operation.length(); // Now safe

        if (b == 0) {
            System.out.println("Cannot divide by zero");
            return 0;
        }
        result = a / b;
        lastOperation = "division";
        return result;
    }
    
    // Method with unused variable and poor naming
    public int multiply(int x,int y){
        int temp=0;
        int unusedVar = 100;
        result=x*y;
        lastOperation="multiplication";
        return result;
    }
    
    // Method with magic numbers and long parameter list - REFACTORED: Used constants
    public double calculateComplexFormula(int a, int b, int c, int d, int e, int f) {
        return (a * PI + b * E + c * SQRT_2 + d * SQRT_3 + e * EULER_MASCHERONI + f * GOLDEN_RATIO);
    }
    
    // Getter with style issues
    public int getResult() {
        return result;
    }

    // Getter for lastOperation to fix "unread field" issue
    public String getLastOperation() {
        return lastOperation;
    }
    
    // Method with dead code
    public void resetCalculator() {
        result = 0;
        lastOperation = "";
        
        // Dead code - never executed
        if (false) {
            System.out.println("This will never print");
        }
    }
    
    // Method with poor exception handling
    public int parseNumber(String str) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            // Poor exception handling - catching generic Exception
            return -1;
        }
    }
}
