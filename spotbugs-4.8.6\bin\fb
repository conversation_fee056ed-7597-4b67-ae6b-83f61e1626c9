#! /bin/sh

# Launch FindBugs from the command line.

escape_arg() {
	echo "$1" | sed -e "s,\\([\\\"' 	]\\),\\\\\\1,g"
}

program="$0"

# Follow symlinks until we get to the actual file.
while [ -h "$program" ]; do
	link=`ls -ld "$program"`
	link=`expr "$link" : '.*-> \(.*\)'`
	if [ "`expr "$link" : '/.*'`" = 0 ]; then
		# Relative
		dir=`dirname "$program"`
		program="$dir/$link"
	else
		# Absolute
		program="$link"
	fi
done

# Assume SpotBugs home directory is the parent
# of the directory containing the script (which should
# normally be "$spotbugs_home/bin").
dir=`dirname "$program"`
spotbugs_home="$dir/.."

# Handle FHS-compliant installations (e.g., Fink)
if [ -d "$spotbugs_home/share/spotbugs" ]; then
	spotbugs_home="$spotbugs_home/share/spotbugs"
fi

# Make absolute
spotbugs_home=`cd "$spotbugs_home" && pwd`

fb_pathsep=':'

# <PERSON>le cygwin, courtesy of <PERSON>out
fb_osname=`uname`
if [ `expr "$fb_osname" : CYGWIN` -ne 0 ]; then
	spotbugs_home=`cygpath --mixed "$spotbugs_home"`
	fb_pathsep=';'
fi
# Handle MKS, courtesy of Kelly O'Hair
if [ "${fb_osname}" = "Windows_NT" ]; then
	fb_pathsep=';'
fi

if [ ! -d "$spotbugs_home" ]; then
	echo "The path $spotbugs_home,"
	echo "which is where I think SpotBugs is located,"
	echo "does not seem to be a directory."
	exit 1
fi

# Choose default java binary
fb_javacmd=java
if [ ! -z "$JAVA_HOME" ] && [ -x "$JAVA_HOME/bin/java" ]; then
	if [ `expr "$fb_osname" : CYGWIN` -ne 0 ]; then
		fb_javacmd=`cygpath --mixed "$JAVA_HOME"`/bin/java
	else
		fb_javacmd="$JAVA_HOME/bin/java"
	fi
fi


fb_appjar="$spotbugs_home/lib/spotbugs.jar"

ShowHelpAndExit() {
	fb_mainclass="edu.umd.cs.findbugs.ShowHelp"
	fb_javacmd=${fb_javacmd:-"java"}
fb_maxheap=${fb_maxheap:-"-Xmx768m"}
fb_appjar=${fb_appjar:-"$spotbugs_home/lib/spotbugs.jar"}
if [ -n "$CLASSPATH" ]; then
	fb_classpath=$fb_appjar$fb_pathsep$CLASSPATH
else
	fb_classpath=$fb_appjar
fi
set -f
#echo command: \
exec "$fb_javacmd" \
	-classpath "$fb_classpath" \
	-Dspotbugs.home="$spotbugs_home"\
	$fb_maxheap $fb_jvmargs $fb_mainclass ${@:+"$@"} $fb_appargs
	exit 0
}

# Set defaults
fb_mainclass="edu.umd.cs.findbugs.workflow.FB"
user_jvmargs=''
ea_arg=''
debug_arg=''
conservespace_arg=''
workhard_arg=''
user_props=''

# Handle command line arguments.
while [ $# -gt 0 ]; do
	case $1 in
	-textui)
		fb_mainclass="edu.umd.cs.findbugs.FindBugs2"
		;;

	-jvmArgs)
		shift
		user_jvmargs="$1"
		;;
		
	-ea)
		ea_arg='-ea'
		;;

	-maxHeap)
		shift
		fb_maxheap="-Xmx$1m"
		;;

	-javahome)
		shift
		fb_javacmd="$1/bin/java"
		;;

	-debug)
		debug_arg="-Dfindbugs.debug=true"
		;;

	-conserveSpace)
		conservespace_arg="-Dfindbugs.conserveSpace=true"
		;;

	-property)
		shift
		user_props="-D$1 $user_props"
		;;
	
	-D*=*)
		user_props="$1 $user_props"
		;;

	-version)
		fb_mainclass=edu.umd.cs.findbugs.Version
		fb_appargs="-release"
		while [ $# -gt 0 ]; do
			shift
		done
		fb_javacmd=${fb_javacmd:-"java"}
fb_maxheap=${fb_maxheap:-"-Xmx768m"}
fb_appjar=${fb_appjar:-"$spotbugs_home/lib/spotbugs.jar"}
if [ -n "$CLASSPATH" ]; then
	fb_classpath=$fb_appjar$fb_pathsep$CLASSPATH
else
	fb_classpath=$fb_appjar
fi
set -f
#echo command: \
exec "$fb_javacmd" \
	-classpath "$fb_classpath" \
	-Dspotbugs.home="$spotbugs_home"\
	$fb_maxheap $fb_jvmargs $fb_mainclass ${@:+"$@"} $fb_appargs
		exit 0
		;;

	-help)
		ShowHelpAndExit
		;;

	# All unrecognized arguments will be accumulated and
	# passed to the application.
	*)
		fb_appargs="$fb_appargs `escape_arg "$1"`"
		;;
	esac

	shift
done

fb_jvmargs="$user_jvmargs $debug_arg $conservespace_arg $workhard_arg $user_props $ea_arg"
if [ $maxheap ]; then
  fb_maxheap="-Xmx${maxheap}m"
fi

# Extra JVM args for MacOSX.
if [ $fb_osname = "Darwin" ]; then
	fb_jvmargs="$fb_jvmargs \
		-Xdock:name=FindBugs -Xdock:icon=${spotbugs_home}/lib/buggy.icns \
		-Dapple.laf.useScreenMenuBar=true"
fi

fb_javacmd=${fb_javacmd:-"java"}
fb_maxheap=${fb_maxheap:-"-Xmx768m"}
fb_appjar=${fb_appjar:-"$spotbugs_home/lib/spotbugs.jar"}
if [ -n "$CLASSPATH" ]; then
	fb_classpath=$fb_appjar$fb_pathsep$CLASSPATH
else
	fb_classpath=$fb_appjar
fi
set -f
#echo command: \
exec "$fb_javacmd" \
	-classpath "$fb_classpath" \
	-Dspotbugs.home="$spotbugs_home"\
	$fb_maxheap $fb_jvmargs $fb_mainclass ${@:+"$@"} $fb_appargs

# vim:ts=3
