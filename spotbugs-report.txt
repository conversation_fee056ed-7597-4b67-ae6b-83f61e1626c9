H B ES: Comparison of String parameter using == or != in com.example.Student.hasCourseName(String)  At Student.java:[line 76]
H C NP: Null pointer dereference of ? in com.example.Calculator.divide(int, int)  Dereferenced at Calculator.java:[line 33]
H B CNT: Rough value of Math.PI found: 3.14159  At Calculator.java:[line 55]
M B CNT: Rough value of Math.E found: 2.71828  At Calculator.java:[line 55]
M P UrF: Unread field: com.example.Calculator.lastOperation  At Calculator.java:[line 14]
