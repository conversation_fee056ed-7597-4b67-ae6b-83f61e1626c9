# Java Quality Analysis Demo Project

## نظرة عامة
هذا مشروع Java بسيط تم إنشاؤه لتوضيح استخدام أدوات تحليل جودة الكود:
- **Checkstyle** - لفحص أسلوب الكتابة
- **SpotBugs** - لاكتشاف الأخطاء والمشاكل
- **Manual Refactoring** - لإصلاح Code Smells

## بنية المشروع
```
src/main/java/com/example/
├── Calculator.java    # فئة العمليات الحسابية
├── Student.java       # فئة إدارة بيانات الطلاب
└── Main.java         # الفئة الرئيسية
```

## كيفية تشغيل المشروع

### 1. تجميع الكود
```bash
javac -d classes -cp . src/main/java/com/example/*.java
```

### 2. تشغيل البرنامج
```bash
java -cp classes com.example.Main
```

### 3. تشغيل Checkstyle
```bash
java -jar checkstyle-11.0.1-all.jar -c checkstyle.xml src/main/java/com/example/
```

### 4. تشغيل SpotBugs
```bash
java -jar spotbugs-4.8.6/lib/spotbugs.jar -textui -output report.txt classes
```

## التحسينات المطبقة

### Checkstyle Fixes:
- ✅ إصلاح مشاكل المسافات البيضاء
- ✅ استبدال import * بـ imports محددة
- ✅ تغيير متغيرات public إلى private

### SpotBugs Fixes:
- ✅ إصلاح Null Pointer Dereference
- ✅ إصلاح String comparison باستخدام equals()
- ✅ إضافة getter للمتغيرات غير المستخدمة

### Code Smells Refactoring:
- ✅ تقسيم Long Methods إلى methods أصغر
- ✅ إزالة Duplicate Code
- ✅ استبدال Magic Numbers بـ Constants

## الملفات المهمة
- `تقرير_تحليل_جودة_الكود.md` - التقرير الشامل
- `checkstyle.xml` - تكوين Checkstyle
- `spotbugs-report.txt` - تقرير SpotBugs الأولي
- `spotbugs-report-final.txt` - تقرير SpotBugs النهائي

## المتطلبات
- Java 11 أو أحدث
- Checkstyle 11.0.1
- SpotBugs 4.8.6

## المؤلف
تم إنشاء هذا المشروع كجزء من واجب تحليل جودة الكود.

## التاريخ
29 سبتمبر 2025
