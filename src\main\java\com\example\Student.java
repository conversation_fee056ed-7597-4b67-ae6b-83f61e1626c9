package com.example;

import java.util.ArrayList;
import java.util.List;

// Student class with code smells and style issues
public class Student {
    private String name; // Now private
    private int age;     // Now private
    private List<String> courses;
    
    // Constructor with style issues
    public Student(String name,int age){
        this.name=name;
        this.age=age;
        this.courses=new ArrayList<String>();
    }
    
    // REFACTORED: Broken down into smaller methods
    public void processStudentData() {
        if (!validateStudentData()) {
            return;
        }

        String grade = calculateGrade();
        printStudentInfo(grade);
        addDefaultCourses(grade);
        printCourses();
    }

    // Extracted validation logic
    private boolean validateStudentData() {
        if (name == null || name.length() == 0) {
            System.out.println("Invalid name");
            return false;
        }

        if (age < 0 || age > 150) {
            System.out.println("Invalid age");
            return false;
        }
        return true;
    }

    // Extracted grade calculation logic
    private String calculateGrade() {
        if (age >= 18) {
            return "College";
        } else if (age >= 14) {
            return "High School";
        } else if (age >= 11) {
            return "Middle School";
        } else {
            return "Elementary";
        }
    }

    // Extracted printing logic
    private void printStudentInfo(final String grade) {
        System.out.println("Student: " + name);
        System.out.println("Age: " + age);
        System.out.println("Grade: " + grade);
    }

    // Extracted course assignment logic
    private void addDefaultCourses(final String grade) {
        if (grade.equals("College")) {
            courses.add("Math");
            courses.add("Science");
            courses.add("English");
        } else if (grade.equals("High School")) {
            courses.add("Algebra");
            courses.add("Biology");
            courses.add("Literature");
        }
    }

    // Extracted course printing logic
    private void printCourses() {
        System.out.println("Courses:");
        for (String course : courses) {
            System.out.println("- " + course);
        }
    }
    
    // Method with potential array index out of bounds
    public String getFirstCourse() {
        String[] courseArray = courses.toArray(new String[0]);
        return courseArray[0]; // Potential IndexOutOfBoundsException
    }
    
    // Method with string comparison issue - FIXED
    public boolean hasCourseName(String courseName) {
        for (String course : courses) {
            if (course.equals(courseName)) { // Fixed: Use .equals() instead of ==
                return true;
            }
        }
        return false;
    }
    
    // Duplicate code - similar to hasCourseName
    public boolean containsCourse(String courseName) {
        for (String course : courses) {
            if (course.equals(courseName)) {
                return true;
            }
        }
        return false;
    }
}
