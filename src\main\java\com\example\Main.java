package com.example;

// Main class with style issues
public class Main{
    
    // Main method with style issues
    public static void main(String[]args){
        System.out.println("Starting Calculator and Student Demo");
        
        // Create calculator instance
        Calculator calc=new Calculator();
        
        // Test calculator methods
        int sum=calc.add(5,3);
        System.out.println("5 + 3 = "+sum);
        
        int product=calc.multiply(4,6);
        System.out.println("4 * 6 = "+product);
        
        // This will cause NPE
        try{
            int division=calc.divide(10,2);
            System.out.println("10 / 2 = "+division);
        }catch(Exception e){
            System.out.println("Error in division: "+e.getMessage());
        }
        
        // Create student instance
        Student student=new Student("Ahmed",20);
        student.processStudentData();
        
        // Test potential array out of bounds
        try{
            String firstCourse=student.getFirstCourse();
            System.out.println("First course: "+firstCourse);
        }catch(Exception e){
            System.out.println("Error getting first course: "+e.getMessage());
        }
        
        System.out.println("Demo completed");
    }
}
