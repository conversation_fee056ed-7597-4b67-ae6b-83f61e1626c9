#! /bin/sh

# A convenient way to call the main() method of a class
# in findbugs.jar.

program="$0"

# Follow symlinks until we get to the actual file.
while [ -h "$program" ]; do
	link=`ls -ld "$program"`
	link=`expr "$link" : '.*-> \(.*\)'`
	if [ "`expr "$link" : '/.*'`" = 0 ]; then
		# Relative
		dir=`dirname "$program"`
		program="$dir/$link"
	else
		# Absolute
		program="$link"
	fi
done

# Assume SpotBugs home directory is the parent
# of the directory containing the script (which should
# normally be "$spotbugs_home/bin").
dir=`dirname "$program"`
spotbugs_home="$dir/.."

# Handle FHS-compliant installations (e.g., Fink)
if [ -d "$spotbugs_home/share/spotbugs" ]; then
	spotbugs_home="$spotbugs_home/share/spotbugs"
fi

# Make absolute
spotbugs_home=`cd "$spotbugs_home" && pwd`

fb_pathsep=':'

# <PERSON>le cygwin, courtesy of <PERSON>out
fb_osname=`uname`
if [ `expr "$fb_osname" : CYGWIN` -ne 0 ]; then
	spotbugs_home=`cygpath --mixed "$spotbugs_home"`
	fb_pathsep=';'
fi
# Handle MKS, courtesy of Kelly O'Hair
if [ "${fb_osname}" = "Windows_NT" ]; then
	fb_pathsep=';'
fi

if [ ! -d "$spotbugs_home" ]; then
	echo "The path $spotbugs_home,"
	echo "which is where I think SpotBugs is located,"
	echo "does not seem to be a directory."
	exit 1
fi

# Choose default java binary
fb_javacmd=java
if [ ! -z "$JAVA_HOME" ] && [ -x "$JAVA_HOME/bin/java" ]; then
	if [ `expr "$fb_osname" : CYGWIN` -ne 0 ]; then
		fb_javacmd=`cygpath --mixed "$JAVA_HOME"`/bin/java
	else
		fb_javacmd="$JAVA_HOME/bin/java"
	fi
fi

if [ $# -eq 0 ]; then
	echo "Usage: fbwrap <main class name> <args...>"
	exit 1
fi

fb_mainclass="$1"
shift

fb_javacmd=${fb_javacmd:-"java"}
fb_maxheap=${fb_maxheap:-"-Xmx768m"}
fb_appjar=${fb_appjar:-"$spotbugs_home/lib/spotbugs.jar"}
if [ -n "$CLASSPATH" ]; then
	fb_classpath=$fb_appjar$fb_pathsep$CLASSPATH
else
	fb_classpath=$fb_appjar
fi
set -f
#echo command: \
exec "$fb_javacmd" \
	-classpath "$fb_classpath" \
	-Dspotbugs.home="$spotbugs_home"\
	$fb_maxheap $fb_jvmargs $fb_mainclass ${@:+"$@"} $fb_appargs

# vim:ts=3
