#! /bin/sh

#
# Simplified SpotBugs startup script.
# This is an experiment.
#

program="$0"

# Follow symlinks until we get to the actual file.
while [ -h "$program" ]; do
	link=`ls -ld "$program"`
	link=`expr "$link" : '.*-> \(.*\)'`
	if [ "`expr "$link" : '/.*'`" = 0 ]; then
		# Relative
		dir=`dirname "$program"`
		program="$dir/$link"
	else
		# Absolute
		program="$link"
	fi
done

# Assume SpotBugs home directory is the parent
# of the directory containing the script (which should
# normally be "$spotbugs_home/bin").
dir=`dirname "$program"`
spotbugs_home="$dir/.."

# Handle FHS-compliant installations (e.g., Fink)
if [ -d "$spotbugs_home/share/spotbugs" ]; then
	spotbugs_home="$spotbugs_home/share/spotbugs"
fi

# Make absolute
spotbugs_home=`cd "$spotbugs_home" && pwd`

fb_pathsep=':'

# <PERSON>le cygwin, courtesy of <PERSON>
fb_osname=`uname`
if [ `expr "$fb_osname" : CYGWIN` -ne 0 ]; then
	spotbugs_home=`cygpath --mixed "$spotbugs_home"`
	fb_pathsep=';'
fi
# Handle MKS, courtesy of Kelly O'Hair
if [ "${fb_osname}" = "Windows_NT" ]; then
	fb_pathsep=';'
fi

if [ ! -d "$spotbugs_home" ]; then
	echo "The path $spotbugs_home,"
	echo "which is where I think SpotBugs is located,"
	echo "does not seem to be a directory."
	exit 1
fi

# Choose default java binary
fb_javacmd=java
if [ ! -z "$JAVA_HOME" ] && [ -x "$JAVA_HOME/bin/java" ]; then
	if [ `expr "$fb_osname" : CYGWIN` -ne 0 ]; then
		fb_javacmd=`cygpath --mixed "$JAVA_HOME"`/bin/java
	else
		fb_javacmd="$JAVA_HOME/bin/java"
	fi
fi

# Default UI is GUI2
fb_launchui="2"

#
# Stuff we're going to pass to the JVM as JVM arguments.
#
jvm_debug=""
jvm_maxheap="-Xmx768m"
jvm_ea=""
jvm_conservespace=""
jvm_user_props=""

#
# Process command line args until we hit one we don't recognize.
#
finishedArgs=false
while [ $# -gt 0 ] && [ "$finishedArgs" = "false" ]; do

	arg=$1

	case $arg in
		-textui)
			shift
			fb_launchui="0"
			;;

		-gui)
			shift
			fb_launchui="2"
			;;

		-gui1)
			shift
			fb_launchui="1"
			;;

		-maxHeap)
			shift
			jvm_maxheap="-Xmx$1m"
			shift
			;;

		-ea)
			shift
			jvm_ea="-ea"
			;;

		-javahome)
			shift
			fb_javacmd="$1/bin/java"
			shift
			;;

		-debug)
			shift
			jvm_debug="-Dfindbugs.debug=true"
			;;

		-conserveSpace)
			shift
			jvm_conservespace="-Dfindbugs.conserveSpace=true"
			;;

		-property)
			shift
			jvm_user_props="-D$1 $jvm_user_props"
			shift
			;;
	
		-D*=*)
			jvm_user_props="$1 $user_props"
			shift
			;;

		-version)
			shift
			fb_launchui="version"
			;;

		-help)
			shift
			fb_launchui="help"
			;;

		# All arguments starting from the first unrecognized arguments
		# are passed on to the Java app.
		*)
			finishedArgs=true
			;;
	esac

done

# Extra JVM args for MacOSX.
if [ $fb_osname = "Darwin" ]; then
	fb_jvmargs="$fb_jvmargs \
		-Xdock:name=SpotBugs -Xdock:icon=${spotbugs_home}/lib/buggy.icns \
		-Dapple.laf.useScreenMenuBar=true"
fi

#
# Launch JVM
#
exec "$fb_javacmd" \
	-classpath "$fb_appjar$fb_pathsep$CLASSPATH" \
	-Dspotbugs.home="$spotbugs_home" \
	$jvm_debug $jvm_maxheap $jvm_ea $jvm_conservespace $jvm_user_props \
	-Dfindbugs.launchUI=$fb_launchui \
	-jar $spotbugs_home/lib/spotbugs.jar \
	${@:+"$@"}
